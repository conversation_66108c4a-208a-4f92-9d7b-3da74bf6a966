import sys
import shutil
import io
import token
import tokenize
import ast
import os

def removeDocstrings(sourceCode):
    try:
        tree = ast.parse(sourceCode)
    except SyntaxError:
        return sourceCode  # fallback nếu lỗi cú pháp

    class DocstringRemover(ast.NodeTransformer):
        def visit_FunctionDef(self, node):
            self.generic_visit(node)
            if (node.body and isinstance(node.body[0], ast.Expr)
                and isinstance(node.body[0].value, ast.Constant)
                and isinstance(node.body[0].value.value, str)):
                node.body.pop(0)
            return node

        def visit_AsyncFunctionDef(self, node):
            return self.visit_FunctionDef(node)

        def visit_ClassDef(self, node):
            self.generic_visit(node)
            if (node.body and isinstance(node.body[0], ast.Expr)
                and isinstance(node.body[0].value, ast.Constant)
                and isinstance(node.body[0].value.value, str)):
                node.body.pop(0)
            return node

        def visit_Module(self, node):
            self.generic_visit(node)
            if (node.body and isinstance(node.body[0], ast.Expr)
                and isinstance(node.body[0].value, ast.Constant)
                and isinstance(node.body[0].value.value, str)):
                node.body.pop(0)
            return node

    remover = DocstringRemover()
    cleanedTree = remover.visit(tree)
    ast.fix_missing_locations(cleanedTree)

    try:
        import astor
        return astor.to_source(cleanedTree)
    except ImportError:
        try:
            return ast.unparse(cleanedTree)
        except Exception:
            return sourceCode  # fallback

def removeComments(code):
    output = []
    ioObj = io.StringIO(code)
    lastLineno = -1
    lastCol = 0
    try:
        tokens = tokenize.generate_tokens(ioObj.readline)
        for toktype, toktext, (srow, scol), (erow, ecol), _ in tokens:
            if toktype == tokenize.COMMENT:
                continue
            if srow > lastLineno:
                lastCol = 0
            if scol > lastCol:
                output.append(" " * (scol - lastCol))
            output.append(toktext)
            lastCol = ecol
            lastLineno = erow
    except tokenize.TokenError:
        return code  # fallback nếu lỗi
    return ''.join(output)

def backupFile(path):
    base, ext = os.path.splitext(path)
    backupPath = f"{base}_backup{ext}"
    shutil.copy2(path, backupPath)
    print(f"✅ Backup created: {backupPath}")

def safeCompileCheck(code, name):
    try:
        compile(code, filename=name, mode="exec")
        return True
    except Exception as e:
        print(f"❌ Compile failed for {name}: {e}")
        return False

def astStructure(code):
    try:
        tree = ast.parse(code)
        return ast.dump(tree)
    except Exception:
        return None

def cleanFile(path, verify=True, dryRun=False):
    with open(path, 'r', encoding='utf-8') as f:
        originalCode = f.read()

    if not safeCompileCheck(originalCode, "original"):
        print("⚠️ Original code is not compilable. Aborting.")
        return

    backupFile(path)

    astBefore = astStructure(originalCode)
    codeNoDoc = removeDocstrings(originalCode)
    cleanedCode = removeComments(codeNoDoc)

    if not safeCompileCheck(cleanedCode, "cleaned"):
        print("❌ Cleaned code cannot compile. Aborting and restoring original.")
        return

    astAfter = astStructure(cleanedCode)
    if verify and astBefore != astAfter:
        print("❌ AST mismatch! Cleaned code may alter logic.")
        return

    if dryRun:
        print("✅ Dry-run complete. No changes written.")
        return

    with open(path, 'w', encoding='utf-8') as f:
        f.write(cleanedCode)

    print(f"✅ Cleaned and verified successfully: {path}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python remove.py <input.py> [--dry-run] [--no-verify]")
        sys.exit(1)

    inputPath = sys.argv[1]
    dryRun = '--dry-run' in sys.argv
    verify = '--no-verify' not in sys.argv

    if not os.path.isfile(inputPath):
        print(f"❌ File not found: {inputPath}")
        sys.exit(1)

    cleanFile(inputPath, verify=verify, dryRun=dryRun)
