#!/usr/bin/env python3
"""
Test script to verify LinkNgon and TrafficUser API authentication fix
"""

import requests
import json
import jwt
from crypto_utils import CryptoUtils
import time

def test_api_endpoints():
    """Test the LinkNgon and TrafficUser API endpoints"""
    
    # Create a test token
    test_user_id = "test_user_123"
    current_time = int(time.time())
    
    # Create auth data similar to what the bot would create
    auth_data = {
        'auth': 'valid',
        'uid': test_user_id,
        'ip': '127.0.0.1',
        'ua': 'Test User Agent',
        'time': current_time,
        'redirectToken': None,
        'commandUserId': test_user_id
    }
    
    # Encrypt and create JWT token
    encrypted_auth_data = CryptoUtils.encrypt(json.dumps(auth_data))
    test_token = CryptoUtils.createJwt(encrypted_auth_data)
    
    print(f"Created test token: {test_token[:50]}...")
    print(f"Testing with user ID: {test_user_id}")
    print("="*60)
    
    # Test LinkNgon redirect endpoint
    print("\n1. Testing LinkNgon redirect endpoint...")
    try:
        linkngon_url = "http://localhost:2503/api/linkngon/redirect"
        params = {'token': test_token}
        response = requests.get(linkngon_url, params=params, timeout=10, allow_redirects=False)
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 302:
            print(f"Redirect Location: {response.headers.get('Location', 'No location header')}")
            print("✅ LinkNgon redirect endpoint working!")
        else:
            print(f"Response: {response.text}")
            if "TOKEN ALREADY USED" in response.text:
                print("❌ Still getting TOKEN ALREADY USED error")
            else:
                print("❓ Different error occurred")
                
    except Exception as e:
        print(f"❌ Error testing LinkNgon: {e}")
    
    print("\n" + "="*60)
    
    # Test TrafficUser redirect endpoint  
    print("\n2. Testing TrafficUser redirect endpoint...")
    try:
        trafficuser_url = "http://localhost:2503/api/trafficuser/redirect"
        params = {'token': test_token}
        response = requests.get(trafficuser_url, params=params, timeout=10, allow_redirects=False)
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 302:
            print(f"Redirect Location: {response.headers.get('Location', 'No location header')}")
            print("✅ TrafficUser redirect endpoint working!")
        else:
            print(f"Response: {response.text}")
            if "TOKEN ALREADY USED" in response.text:
                print("❌ Still getting TOKEN ALREADY USED error")
            else:
                print("❓ Different error occurred")
                
    except Exception as e:
        print(f"❌ Error testing TrafficUser: {e}")

if __name__ == "__main__":
    print("Testing LinkNgon and TrafficUser API authentication fix...")
    print("Make sure the server is running on localhost:2503")
    print()
    
    test_api_endpoints()
    
    print("\n" + "="*60)
    print("Test completed!")
